FROM envoyproxy/envoy:v1.27-latest

# Install envsubst for environment variable substitution
RUN apt-get update && apt-get install -y gettext-base && rm -rf /var/lib/apt/lists/*

# Copy Envoy configuration template
COPY ./render/envoy-render.yaml.template /etc/envoy/envoy.yaml.template

# Create startup script
RUN cat > /usr/local/bin/start-envoy.sh << 'EOF'
#!/bin/bash
set -e

# Substitute environment variables in Envoy config
envsubst < /etc/envoy/envoy.yaml.template > /etc/envoy/envoy.yaml

# Start Envoy
exec /usr/local/bin/envoy -c /etc/envoy/envoy.yaml
EOF

RUN chmod +x /usr/local/bin/start-envoy.sh

# Expose Envoy ports
EXPOSE 10000 9901

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD wget --quiet --tries=1 --spider http://localhost:9901/ready || exit 1

# Start Envoy with environment substitution
CMD ["/usr/local/bin/start-envoy.sh"]
