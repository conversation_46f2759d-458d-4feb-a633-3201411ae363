services:
  # PostgreSQL Database for Auth Service
  - type: pserv
    name: postgres-auth
    env: docker
    plan: starter
    dockerfilePath: ./render/postgres-auth.Dockerfile
    envVars:
      - key: POSTGRES_USER
        value: postgres
      - key: POSTGRES_PASSWORD
        generateValue: true
      - key: POSTGRES_DB
        value: auth_service
    disk:
      name: postgres-auth-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  # PostgreSQL Database for Admin Service
  - type: pserv
    name: postgres-admin
    env: docker
    plan: starter
    dockerfilePath: ./render/postgres-admin.Dockerfile
    envVars:
      - key: POSTGRES_USER
        value: postgres
      - key: POSTGRES_PASSWORD
        generateValue: true
      - key: POSTGRES_DB
        value: admin_service
    disk:
      name: postgres-admin-data
      mountPath: /var/lib/postgresql/data
      sizeGB: 10

  # Redis Cache Service
  - type: pserv
    name: redis
    env: docker
    plan: starter
    dockerfilePath: ./render/redis.Dockerfile
    disk:
      name: redis-data
      mountPath: /data
      sizeGB: 5

  # Kafka Message Broker
  - type: pserv
    name: kafka
    env: docker
    plan: starter
    dockerfilePath: ./render/kafka.Dockerfile
    envVars:
      - key: KAFKA_ZOOKEEPER_CONNECT
        value: zookeeper:2181
      - key: KAFKA_ADVERTISED_LISTENERS
        value: PLAINTEXT://kafka:9092
      - key: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
        value: 1
    disk:
      name: kafka-data
      mountPath: /var/lib/kafka/data
      sizeGB: 10

  # Zookeeper for Kafka
  - type: pserv
    name: zookeeper
    env: docker
    plan: starter
    dockerfilePath: ./render/zookeeper.Dockerfile
    envVars:
      - key: ZOOKEEPER_CLIENT_PORT
        value: 2181
      - key: ZOOKEEPER_TICK_TIME
        value: 2000
    disk:
      name: zookeeper-data
      mountPath: /var/lib/zookeeper/data
      sizeGB: 5

  # Auth Service
  - type: web
    name: auth-service
    env: docker
    plan: starter
    dockerfilePath: ./auth-service/Dockerfile
    envVars:
      - key: POSTGRES_SERVER
        fromService:
          type: pserv
          name: postgres-auth
          property: host
      - key: POSTGRES_USER
        value: postgres
      - key: POSTGRES_PASSWORD
        fromService:
          type: pserv
          name: postgres-auth
          envVarKey: POSTGRES_PASSWORD
      - key: POSTGRES_DB
        value: auth_service
      - key: REDIS_HOST
        fromService:
          type: pserv
          name: redis
          property: host
      - key: REDIS_PORT
        value: 6379
      - key: KAFKA_BOOTSTRAP_SERVERS
        fromService:
          type: pserv
          name: kafka
          property: connectionString
      - key: JWT_SECRET_KEY
        generateValue: true
      - key: RSA_PRIVATE_KEY
        sync: false
      - key: RSA_PUBLIC_KEY
        sync: false
    healthCheckPath: /health
    startCommand: sh -c "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 10000"

  # Admin Service
  - type: web
    name: admin-service
    env: docker
    plan: starter
    dockerfilePath: ./admin-service/Dockerfile
    envVars:
      - key: POSTGRES_SERVER
        fromService:
          type: pserv
          name: postgres-admin
          property: host
      - key: POSTGRES_USER
        value: postgres
      - key: POSTGRES_PASSWORD
        fromService:
          type: pserv
          name: postgres-admin
          envVarKey: POSTGRES_PASSWORD
      - key: POSTGRES_DB
        value: admin_service
      - key: KAFKA_BOOTSTRAP_SERVERS
        fromService:
          type: pserv
          name: kafka
          property: connectionString
      - key: AUTH_SERVICE_URL
        fromService:
          type: web
          name: auth-service
          property: host
      - key: JWT_SECRET_KEY
        fromService:
          type: web
          name: auth-service
          envVarKey: JWT_SECRET_KEY
      - key: RSA_PRIVATE_KEY
        sync: false
      - key: RSA_PUBLIC_KEY
        sync: false
    healthCheckPath: /health
    startCommand: sh -c "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 10000"

  # APISIX Gateway for Auth Service
  - type: web
    name: apisix-gateway
    env: docker
    plan: starter
    dockerfilePath: ./render/apisix.Dockerfile
    envVars:
      - key: AUTH_SERVICE_HOST
        fromService:
          type: web
          name: auth-service
          property: host
      - key: APISIX_STAND_ALONE
        value: true
    healthCheckPath: /health
    startCommand: /usr/local/apisix/bin/apisix start

  # Envoy Gateway for Admin Service
  - type: web
    name: envoy-gateway
    env: docker
    plan: starter
    dockerfilePath: ./render/envoy.Dockerfile
    envVars:
      - key: ADMIN_SERVICE_HOST
        fromService:
          type: web
          name: admin-service
          property: host
    healthCheckPath: /ready
    startCommand: /usr/local/bin/envoy -c /etc/envoy/envoy.yaml

databases: []
