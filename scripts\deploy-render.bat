@echo off
setlocal enabledelayedexpansion

echo 🚀 ZCare Microservices - Render Deployment Preparation
echo =====================================================
echo.

REM Check if OpenSSL is available
where openssl >nul 2>nul
if %errorlevel% neq 0 (
    REM Try Git for Windows OpenSSL location
    if exist "C:\Program Files\Git\usr\bin\openssl.exe" (
        set "PATH=C:\Program Files\Git\usr\bin;%PATH%"
        echo ℹ️  Using OpenSSL from Git for Windows
    ) else if exist "C:\Program Files (x86)\Git\usr\bin\openssl.exe" (
        set "PATH=C:\Program Files (x86)\Git\usr\bin;%PATH%"
        echo ℹ️  Using OpenSSL from Git for Windows
    ) else (
        echo ❌ OpenSSL is required but not found
        echo Please install Git for Windows which includes OpenSSL
        echo Or install OpenSSL separately and add to PATH
        pause
        exit /b 1
    )
)

REM Check if Git is available
where git >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Git is required but not found in PATH
    echo Please install Git for Windows
    pause
    exit /b 1
)

echo ✅ Requirements check passed
echo.

REM Create keys directory if it doesn't exist
if not exist "keys" (
    mkdir keys
    echo ℹ️  Created keys directory
)

REM Generate RSA keys if they don't exist
if not exist "keys\private.pem" (
    echo ℹ️  Generating RSA private key...
    openssl genrsa -out keys\private.pem 2048
    if %errorlevel% neq 0 (
        echo ❌ Failed to generate private key
        pause
        exit /b 1
    )
)

if not exist "keys\public.pem" (
    echo ℹ️  Generating RSA public key...
    openssl rsa -in keys\private.pem -pubout -out keys\public.pem
    if %errorlevel% neq 0 (
        echo ❌ Failed to generate public key
        pause
        exit /b 1
    )
)

echo ✅ RSA keys are ready
echo.

REM Check if render.yaml exists
if not exist "render.yaml" (
    echo ❌ render.yaml not found
    pause
    exit /b 1
)

REM Check if required Dockerfiles exist
set "missing_files="
if not exist "render\postgres-auth.Dockerfile" set "missing_files=!missing_files! postgres-auth.Dockerfile"
if not exist "render\postgres-admin.Dockerfile" set "missing_files=!missing_files! postgres-admin.Dockerfile"
if not exist "render\redis.Dockerfile" set "missing_files=!missing_files! redis.Dockerfile"
if not exist "render\kafka.Dockerfile" set "missing_files=!missing_files! kafka.Dockerfile"
if not exist "render\zookeeper.Dockerfile" set "missing_files=!missing_files! zookeeper.Dockerfile"
if not exist "render\apisix.Dockerfile" set "missing_files=!missing_files! apisix.Dockerfile"
if not exist "render\envoy.Dockerfile" set "missing_files=!missing_files! envoy.Dockerfile"

if not "!missing_files!"=="" (
    echo ❌ Missing Dockerfiles: !missing_files!
    pause
    exit /b 1
)

REM Check service Dockerfiles
if not exist "auth-service\Dockerfile" (
    echo ❌ auth-service\Dockerfile not found
    pause
    exit /b 1
)

if not exist "admin-service\Dockerfile" (
    echo ❌ admin-service\Dockerfile not found
    pause
    exit /b 1
)

echo ✅ All required files found
echo.

REM Check Git status
if not exist ".git" (
    echo ❌ This is not a Git repository
    echo Initialize with: git init
    pause
    exit /b 1
)

echo ✅ Git repository detected
echo.

REM Display environment variables
echo ℹ️  Environment Variables for Render Dashboard:
echo ================================================
echo.
echo Copy these values and set them in your Render service environment variables:
echo.

echo RSA_PRIVATE_KEY:
powershell -Command "[Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((Get-Content 'keys\private.pem' -Raw)))"
echo.

echo RSA_PUBLIC_KEY:
powershell -Command "[Convert]::ToBase64String([System.Text.Encoding]::UTF8.GetBytes((Get-Content 'keys\public.pem' -Raw)))"
echo.

echo ⚠️  Save these values securely - you'll need them for both auth-service and admin-service
echo.

REM Create deployment summary
echo ℹ️  Creating deployment summary...
(
echo # ZCare Microservices - Render Deployment Summary
echo.
echo ## 📅 Deployment Date
echo %date% %time%
echo.
echo ## 🏗️ Services to be Deployed
echo.
echo ### Infrastructure Services
echo - **postgres-auth**: PostgreSQL database for Auth Service
echo - **postgres-admin**: PostgreSQL database for Admin Service
echo - **redis**: Redis cache for Auth Service
echo - **kafka**: Kafka message broker
echo - **zookeeper**: Zookeeper for Kafka coordination
echo.
echo ### Application Services
echo - **auth-service**: Authentication microservice ^(FastAPI^)
echo - **admin-service**: Administration microservice ^(FastAPI^)
echo.
echo ### Gateway Services
echo - **apisix-gateway**: APISIX gateway for Auth Service
echo - **envoy-gateway**: Envoy gateway for Admin Service
echo.
echo ## 🔗 Expected Service URLs
echo.
echo After deployment, your services will be available at:
echo.
echo - **APISIX Gateway**: https://apisix-gateway-[random].onrender.com
echo - **Envoy Gateway**: https://envoy-gateway-[random].onrender.com
echo - **Auth Service**: https://auth-service-[random].onrender.com
echo - **Admin Service**: https://admin-service-[random].onrender.com
echo.
echo ## 🔑 Environment Variables Required
echo.
echo Set these in Render Dashboard for both auth-service and admin-service:
echo.
echo ```
echo RSA_PRIVATE_KEY=^<base64-encoded-private-key^>
echo RSA_PUBLIC_KEY=^<base64-encoded-public-key^>
echo ```
echo.
echo ## 📋 Next Steps
echo.
echo 1. Push your code to GitHub
echo 2. Connect repository to Render
echo 3. Deploy using render.yaml blueprint
echo 4. Set environment variables in Render dashboard
echo 5. Test the deployed services
echo.
echo ## 🔍 Health Check Endpoints
echo.
echo - APISIX Gateway: /health
echo - Envoy Gateway: /health
echo - Auth Service: /health
echo - Admin Service: /health
echo.
echo ## 📚 Documentation
echo.
echo - Full deployment guide: deploy-to-render.md
echo - API documentation available at /docs endpoint on each service
) > deployment-summary.md

echo ✅ Deployment summary created: deployment-summary.md
echo.

echo ✅ Deployment preparation completed!
echo.
echo ℹ️  Next steps:
echo 1. Push your code to GitHub
echo 2. Go to Render Dashboard: https://dashboard.render.com
echo 3. Create new Blueprint and connect your repository
echo 4. Set the environment variables shown above
echo 5. Deploy your services
echo.
echo ℹ️  For detailed instructions, see: deploy-to-render.md
echo.

pause
