FROM confluentinc/cp-kafka:7.4.0

# Set environment variables
ENV KAFKA_BROKER_ID=1
ENV KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
ENV KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
ENV KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
ENV KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
ENV KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1
ENV KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS=0
ENV KAFKA_JMX_PORT=9101
ENV KAFKA_JMX_HOSTNAME=localhost
ENV KAFKA_CONFLUENT_SCHEMA_REGISTRY_URL=http://schema-registry:8081
ENV CONFLUENT_METRICS_REPORTER_BOOTSTRAP_SERVERS=kafka:9092
ENV CONFLUENT_METRICS_REPORTER_TOPIC_REPLICAS=1
ENV CONFLUENT_METRICS_ENABLE=true
ENV CONFLUENT_SUPPORT_CUSTOMER_ID=anonymous

# Create kafka data directory
USER root
RUN mkdir -p /var/lib/kafka/data && \
    chown -R appuser:appuser /var/lib/kafka/data

# Switch back to appuser
USER appuser

# Expose Kafka port
EXPOSE 9092

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD kafka-broker-api-versions --bootstrap-server localhost:9092 || exit 1

# Start Kafka
CMD ["/etc/confluent/docker/run"]
