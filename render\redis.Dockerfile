FROM redis:7.4.4-alpine

# Create redis user and data directory
RUN addgroup -g 999 redis && \
    adduser -D -u 999 -G redis redis && \
    mkdir -p /data && \
    chown -R redis:redis /data

# Copy custom redis configuration if exists
COPY ./render/redis.conf /usr/local/etc/redis/redis.conf 2>/dev/null || echo "# Default Redis config" > /usr/local/etc/redis/redis.conf

# Set proper permissions
RUN chown redis:redis /usr/local/etc/redis/redis.conf

# Expose Redis port
EXPOSE 6379

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
  CMD redis-cli ping || exit 1

# Switch to redis user
USER redis

# Start Redis server
CMD ["redis-server", "/usr/local/etc/redis/redis.conf"]
