# Deploy ZCare Microservices to Render

This guide will help you deploy the ZCare microservices architecture to Render.com with the mixed gateway setup (APISIX for Auth Service, Envoy for Admin Service).

## 🏗️ Architecture Overview

The deployment includes:
- **Auth Service** (FastAPI) with APISIX Gateway
- **Admin Service** (FastAPI) with Envoy Gateway  
- **PostgreSQL** databases (separate for each service)
- **Redis** cache for auth service
- **Kafka + Zookeeper** for event streaming

## 📋 Prerequisites

1. **Render Account**: Sign up at [render.com](https://render.com)
2. **GitHub Repository**: Your code should be in a GitHub repository
3. **RSA Keys**: Generate RSA key pair for JWT signing

## 🔑 Generate RSA Keys

Before deployment, generate RSA keys for JWT signing:

```bash
# Generate private key
openssl genrsa -out keys/private.pem 2048

# Generate public key
openssl rsa -in keys/private.pem -pubout -out keys/public.pem

# Get base64 encoded keys for environment variables
echo "RSA_PRIVATE_KEY:"
cat keys/private.pem | base64 -w 0

echo "RSA_PUBLIC_KEY:"
cat keys/public.pem | base64 -w 0
```

## 🚀 Deployment Steps

### Step 1: Connect Repository to Render

1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click "New +" → "Blueprint"
3. Connect your GitHub repository
4. Select the repository containing this microservices code

### Step 2: Configure Blueprint

1. Render will detect the `render.yaml` file
2. Review the services that will be created:
   - `postgres-auth` - PostgreSQL for Auth Service
   - `postgres-admin` - PostgreSQL for Admin Service  
   - `redis` - Redis cache
   - `kafka` - Kafka message broker
   - `zookeeper` - Zookeeper for Kafka
   - `auth-service` - Auth microservice
   - `admin-service` - Admin microservice
   - `apisix-gateway` - APISIX gateway for auth
   - `envoy-gateway` - Envoy gateway for admin

### Step 3: Set Environment Variables

After deployment, set these environment variables in Render dashboard:

#### For Auth Service:
```
RSA_PRIVATE_KEY=<base64-encoded-private-key>
RSA_PUBLIC_KEY=<base64-encoded-public-key>
```

#### For Admin Service:
```
RSA_PRIVATE_KEY=<base64-encoded-private-key>
RSA_PUBLIC_KEY=<base64-encoded-public-key>
```

### Step 4: Deploy

1. Click "Apply" to start deployment
2. Wait for all services to deploy (this may take 10-15 minutes)
3. Monitor the deployment logs for any issues

## 🌐 Service URLs

After deployment, you'll have these endpoints:

### APISIX Gateway (Auth Service)
- **URL**: `https://apisix-gateway-<random>.onrender.com`
- **Routes**:
  - `GET /` - Gateway info
  - `GET /health` - Health check
  - `POST /api/v1/auth/login` - User login
  - `POST /api/v1/auth/users/` - User registration
  - `POST /api/v1/auth/forgot-password` - Password reset
  - `GET /auth/docs` - Swagger UI

### Envoy Gateway (Admin Service)  
- **URL**: `https://envoy-gateway-<random>.onrender.com`
- **Routes**:
  - `GET /` - Gateway info
  - `GET /health` - Health check
  - `GET /api/v1/admin/*` - Admin API routes
  - `GET /admin/docs` - Swagger UI

### Direct Service Access
- **Auth Service**: `https://auth-service-<random>.onrender.com`
- **Admin Service**: `https://admin-service-<random>.onrender.com`

## 🔧 Configuration Details

### Database Configuration
- **PostgreSQL 13** with persistent storage
- Separate databases for auth and admin services
- Automatic password generation
- Health checks enabled

### Redis Configuration
- **Redis 7.4.4** with persistence
- Optimized for caching with LRU eviction
- 256MB memory limit

### Kafka Configuration
- **Confluent Kafka 7.4.0**
- Single broker setup (suitable for development)
- Automatic topic creation enabled

### Gateway Configuration
- **APISIX**: Standalone mode with rate limiting, CORS, caching
- **Envoy**: HTTP/1.1 and HTTP/2 support with health checks
- Both gateways include monitoring and metrics

## 🔍 Monitoring & Health Checks

All services include health checks:
- **Databases**: Connection and readiness checks
- **Services**: HTTP health endpoints
- **Gateways**: Route availability checks

Monitor your services in the Render dashboard for:
- CPU and memory usage
- Request metrics
- Error rates
- Deployment status

## 🐛 Troubleshooting

### Common Issues:

1. **Service startup failures**:
   - Check environment variables are set correctly
   - Verify RSA keys are properly base64 encoded
   - Check service logs in Render dashboard

2. **Database connection issues**:
   - Ensure database services are healthy
   - Check connection strings in service logs
   - Verify network connectivity between services

3. **Gateway routing issues**:
   - Check gateway configuration templates
   - Verify service hostnames are resolving
   - Test direct service endpoints first

### Debugging Commands:

```bash
# Check service logs
render logs <service-name>

# Check service status
render status <service-name>

# Restart service
render restart <service-name>
```

## 📊 Scaling

To scale your services:

1. Go to Render Dashboard
2. Select the service to scale
3. Change the instance type or add replicas
4. Update load balancer configuration if needed

## 🔒 Security

- All services use HTTPS by default
- Environment variables are encrypted
- Database passwords are auto-generated
- CORS is properly configured
- Rate limiting is enabled on gateways

## 💰 Cost Optimization

- Use **Starter** plans for development
- Upgrade to **Standard** for production
- Monitor usage in Render dashboard
- Consider using shared databases for cost savings

## 🎯 Next Steps

1. Set up custom domains
2. Configure SSL certificates
3. Set up monitoring and alerting
4. Implement CI/CD pipelines
5. Add backup strategies for databases

Your ZCare microservices are now deployed and ready for use! 🎉
