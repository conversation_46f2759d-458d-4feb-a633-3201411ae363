FROM apache/apisix:3.6.0-debian

# Install envsubst for environment variable substitution
RUN apt-get update && apt-get install -y gettext-base && rm -rf /var/lib/apt/lists/*

# Copy configuration templates
COPY ./gateway/apisix/conf/config.yaml /usr/local/apisix/conf/config.yaml
COPY ./render/apisix-render.yaml.template /usr/local/apisix/conf/apisix.yaml.template

# Create startup script
RUN cat > /usr/local/bin/start-apisix.sh << 'EOF'
#!/bin/bash
set -e

# Substitute environment variables in APISIX config
envsubst < /usr/local/apisix/conf/apisix.yaml.template > /usr/local/apisix/conf/apisix.yaml

# Start APISIX
exec /usr/local/apisix/bin/apisix start
EOF

RUN chmod +x /usr/local/bin/start-apisix.sh

# Set environment for standalone mode
ENV APISIX_STAND_ALONE=true

# Expose APISIX ports
EXPOSE 9080 9091 9092

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:9080/health || exit 1

# Start APISIX with environment substitution
CMD ["/usr/local/bin/start-apisix.sh"]
