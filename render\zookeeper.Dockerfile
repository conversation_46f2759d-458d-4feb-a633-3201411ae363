FROM confluentinc/cp-zookeeper:7.4.0

# Set environment variables
ENV ZOOKEEPER_CLIENT_PORT=2181
ENV ZOOKEEPER_TICK_TIME=2000
ENV ZOOKEEPER_INIT_LIMIT=5
ENV ZOOKEEPER_SYNC_LIMIT=2
ENV ZO<PERSON>EEPER_MAX_CLIENT_CNXNS=60
ENV ZOOKEEPER_AUTOPURGE_SNAP_RETAIN_COUNT=3
ENV ZOOKEEPER_AUTOPURGE_PURGE_INTERVAL=24

# Create zookeeper data directory
USER root
RUN mkdir -p /var/lib/zookeeper/data && \
    mkdir -p /var/lib/zookeeper/log && \
    chown -R appuser:appuser /var/lib/zookeeper

# Switch back to appuser
USER appuser

# Expose Zookeeper port
EXPOSE 2181

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD echo ruok | nc localhost 2181 | grep imok || exit 1

# Start Zookeeper
CMD ["/etc/confluent/docker/run"]
