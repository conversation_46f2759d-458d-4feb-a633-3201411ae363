admin:
  address:
    socket_address:
      protocol: TCP
      address: 0.0.0.0
      port_value: 9901

static_resources:
  listeners:
  - name: listener_0
    address:
      socket_address:
        protocol: TCP
        address: 0.0.0.0
        port_value: 10000
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: ingress_http
          access_log:
          - name: envoy.access_loggers.stdout
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.access_loggers.stream.v3.StdoutAccessLog
          http_filters:
          - name: envoy.filters.http.cors
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.cors.v3.Cors
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
          route_config:
            name: local_route
            virtual_hosts:
            - name: local_service
              domains: ["*"]
              cors:
                allow_origin_string_match:
                - prefix: "*"
                allow_methods: GET, PUT, DELETE, POST, OPTIONS
                allow_headers: keep-alive,user-agent,cache-control,content-type,content-transfer-encoding,custom-header-1,x-accept-content-transfer-encoding,x-accept-response-streaming,x-user-agent,x-grpc-web,grpc-timeout,authorization
                max_age: "1728000"
                expose_headers: custom-header-1,grpc-status,grpc-message
              routes:
              - match:
                  prefix: "/api/v1/admin"
                route:
                  cluster: admin_service
                  prefix_rewrite: "/api/v1"
              - match:
                  prefix: "/admin"
                route:
                  cluster: admin_service
                  prefix_rewrite: "/"
              - match:
                  prefix: "/health"
                direct_response:
                  status: 200
                  body:
                    inline_string: "healthy"
              - match:
                  prefix: "/"
                direct_response:
                  status: 200
                  body:
                    inline_string: '{"message": "ZCare API Gateway - Envoy (Admin Service)", "version": "1.0.0", "services": ["admin-service"], "gateway_type": "envoy", "port": 10000}'

  clusters:
  - name: admin_service
    connect_timeout: 30s
    type: LOGICAL_DNS
    dns_lookup_family: V4_ONLY
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: admin_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: ${ADMIN_SERVICE_HOST}
                port_value: 10000
    health_checks:
    - timeout: 5s
      interval: 30s
      unhealthy_threshold: 3
      healthy_threshold: 2
      http_health_check:
        path: "/health"
