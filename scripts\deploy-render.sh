#!/bin/bash

# ZCare Microservices - Render Deployment Script
# This script helps prepare and deploy the microservices to Render.com

set -e

echo "🚀 ZCare Microservices - Render Deployment Preparation"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if required tools are installed
check_requirements() {
    print_info "Checking requirements..."
    
    if ! command -v openssl &> /dev/null; then
        print_error "OpenSSL is required but not installed"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        print_error "Git is required but not installed"
        exit 1
    fi
    
    print_status "All requirements satisfied"
}

# Generate RSA keys if they don't exist
generate_rsa_keys() {
    print_info "Checking RSA keys..."
    
    if [ ! -d "keys" ]; then
        mkdir -p keys
        print_info "Created keys directory"
    fi
    
    if [ ! -f "keys/private.pem" ] || [ ! -f "keys/public.pem" ]; then
        print_info "Generating RSA key pair..."
        
        # Generate private key
        openssl genrsa -out keys/private.pem 2048
        
        # Generate public key
        openssl rsa -in keys/private.pem -pubout -out keys/public.pem
        
        print_status "RSA keys generated successfully"
    else
        print_status "RSA keys already exist"
    fi
}

# Display environment variables that need to be set in Render
show_environment_variables() {
    print_info "Environment Variables for Render Dashboard:"
    echo ""
    echo "Copy these values and set them in your Render service environment variables:"
    echo ""
    
    echo "RSA_PRIVATE_KEY:"
    echo "$(cat keys/private.pem | base64 -w 0)"
    echo ""
    
    echo "RSA_PUBLIC_KEY:"
    echo "$(cat keys/public.pem | base64 -w 0)"
    echo ""
    
    print_warning "Save these values securely - you'll need them for both auth-service and admin-service"
}

# Validate render.yaml configuration
validate_render_config() {
    print_info "Validating render.yaml configuration..."
    
    if [ ! -f "render.yaml" ]; then
        print_error "render.yaml not found"
        exit 1
    fi
    
    # Check if all required Dockerfiles exist
    local dockerfiles=(
        "render/postgres-auth.Dockerfile"
        "render/postgres-admin.Dockerfile"
        "render/redis.Dockerfile"
        "render/kafka.Dockerfile"
        "render/zookeeper.Dockerfile"
        "render/apisix.Dockerfile"
        "render/envoy.Dockerfile"
    )
    
    for dockerfile in "${dockerfiles[@]}"; do
        if [ ! -f "$dockerfile" ]; then
            print_error "Missing Dockerfile: $dockerfile"
            exit 1
        fi
    done
    
    print_status "All Dockerfiles found"
    
    # Check if service Dockerfiles exist
    if [ ! -f "auth-service/Dockerfile" ]; then
        print_error "auth-service/Dockerfile not found"
        exit 1
    fi
    
    if [ ! -f "admin-service/Dockerfile" ]; then
        print_error "admin-service/Dockerfile not found"
        exit 1
    fi
    
    print_status "Service Dockerfiles found"
    print_status "render.yaml configuration is valid"
}

# Check git repository status
check_git_status() {
    print_info "Checking Git repository status..."
    
    if [ ! -d ".git" ]; then
        print_error "This is not a Git repository. Initialize with: git init"
        exit 1
    fi
    
    # Check if there are uncommitted changes
    if [ -n "$(git status --porcelain)" ]; then
        print_warning "You have uncommitted changes. Consider committing them before deployment."
        echo ""
        git status --short
        echo ""
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_info "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Check if we're on a branch that can be pushed
    local current_branch=$(git branch --show-current)
    if [ -z "$current_branch" ]; then
        print_error "Not on a Git branch. Create and switch to a branch first."
        exit 1
    fi
    
    print_status "Git repository is ready"
    print_info "Current branch: $current_branch"
}

# Create deployment summary
create_deployment_summary() {
    print_info "Creating deployment summary..."
    
    cat > deployment-summary.md << EOF
# ZCare Microservices - Render Deployment Summary

## 📅 Deployment Date
$(date)

## 🏗️ Services to be Deployed

### Infrastructure Services
- **postgres-auth**: PostgreSQL database for Auth Service
- **postgres-admin**: PostgreSQL database for Admin Service
- **redis**: Redis cache for Auth Service
- **kafka**: Kafka message broker
- **zookeeper**: Zookeeper for Kafka coordination

### Application Services
- **auth-service**: Authentication microservice (FastAPI)
- **admin-service**: Administration microservice (FastAPI)

### Gateway Services
- **apisix-gateway**: APISIX gateway for Auth Service
- **envoy-gateway**: Envoy gateway for Admin Service

## 🔗 Expected Service URLs

After deployment, your services will be available at:

- **APISIX Gateway**: https://apisix-gateway-[random].onrender.com
- **Envoy Gateway**: https://envoy-gateway-[random].onrender.com
- **Auth Service**: https://auth-service-[random].onrender.com
- **Admin Service**: https://admin-service-[random].onrender.com

## 🔑 Environment Variables Required

Set these in Render Dashboard for both auth-service and admin-service:

\`\`\`
RSA_PRIVATE_KEY=<base64-encoded-private-key>
RSA_PUBLIC_KEY=<base64-encoded-public-key>
\`\`\`

## 📋 Next Steps

1. Push your code to GitHub
2. Connect repository to Render
3. Deploy using render.yaml blueprint
4. Set environment variables in Render dashboard
5. Test the deployed services

## 🔍 Health Check Endpoints

- APISIX Gateway: /health
- Envoy Gateway: /health  
- Auth Service: /health
- Admin Service: /health

## 📚 Documentation

- Full deployment guide: deploy-to-render.md
- API documentation available at /docs endpoint on each service

EOF

    print_status "Deployment summary created: deployment-summary.md"
}

# Main deployment preparation function
main() {
    echo ""
    print_info "Starting deployment preparation..."
    echo ""
    
    check_requirements
    echo ""
    
    generate_rsa_keys
    echo ""
    
    validate_render_config
    echo ""
    
    check_git_status
    echo ""
    
    create_deployment_summary
    echo ""
    
    show_environment_variables
    echo ""
    
    print_status "Deployment preparation completed!"
    echo ""
    print_info "Next steps:"
    echo "1. Push your code to GitHub: git push origin $(git branch --show-current)"
    echo "2. Go to Render Dashboard: https://dashboard.render.com"
    echo "3. Create new Blueprint and connect your repository"
    echo "4. Set the environment variables shown above"
    echo "5. Deploy your services"
    echo ""
    print_info "For detailed instructions, see: deploy-to-render.md"
}

# Run main function
main "$@"
