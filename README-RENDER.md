# 🚀 ZCare Microservices - Render Deployment

Deploy your ZCare microservices architecture to Render.com with mixed gateway setup (APISIX + Envoy).

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐
│   APISIX        │    │   Envoy         │
│   Gateway       │    │   Gateway       │
│   (Port 9080)   │    │   (Port 10000)  │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   Auth Service  │    │  Admin Service  │
│   (FastAPI)     │    │   (FastAPI)     │
│   + Redis Cache │    │                 │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│  PostgreSQL     │    │  PostgreSQL     │
│  (auth_service) │    │ (admin_service) │
└─────────────────┘    └─────────────────┘
          │                      │
          └──────────┬───────────┘
                     ▼
            ┌─────────────────┐
            │ Kafka + Zookeeper│
            │ (Event Streaming)│
            └─────────────────┘
```

## 📦 Services Included

### Infrastructure
- **PostgreSQL** (2 instances) - Separate databases for auth and admin
- **Redis** - Caching for auth service
- **Kafka + Zookeeper** - Event streaming

### Applications  
- **Auth Service** - User authentication, registration, password reset
- **Admin Service** - Administrative functions
- **APISIX Gateway** - API gateway for auth service
- **Envoy Gateway** - API gateway for admin service

## 🚀 Quick Start

### 1. Prepare for Deployment

Run the deployment preparation script:

**Windows:**
```cmd
scripts\deploy-render.bat
```

**Linux/Mac:**
```bash
chmod +x scripts/deploy-render.sh
./scripts/deploy-render.sh
```

This will:
- ✅ Check requirements (OpenSSL, Git)
- 🔑 Generate RSA keys for JWT signing
- 📋 Validate configuration files
- 📊 Create deployment summary
- 🔐 Display environment variables

### 2. Push to GitHub

```bash
git add .
git commit -m "Add Render deployment configuration"
git push origin main
```

### 3. Deploy to Render

1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click **"New +"** → **"Blueprint"**
3. Connect your GitHub repository
4. Select this repository
5. Render will detect `render.yaml` and show all services
6. Click **"Apply"** to start deployment

### 4. Set Environment Variables

In Render Dashboard, set these for **both** `auth-service` and `admin-service`:

```
RSA_PRIVATE_KEY=<base64-encoded-private-key>
RSA_PUBLIC_KEY=<base64-encoded-public-key>
```

*(Values provided by the deployment script)*

## 🌐 Service Endpoints

After deployment, you'll have these URLs:

### 🔐 Auth Service (via APISIX Gateway)
- **Gateway**: `https://apisix-gateway-[random].onrender.com`
- **API Routes**:
  - `POST /api/v1/auth/users/` - User registration
  - `POST /api/v1/auth/login` - User login  
  - `POST /api/v1/auth/forgot-password` - Password reset
  - `POST /api/v1/auth/verify-otp` - OTP verification
  - `POST /api/v1/auth/reset-password` - Password reset
- **Documentation**: `/auth/docs` - Swagger UI
- **Health**: `/health`

### 🛠️ Admin Service (via Envoy Gateway)  
- **Gateway**: `https://envoy-gateway-[random].onrender.com`
- **API Routes**:
  - `GET /api/v1/admin/*` - Admin API endpoints
- **Documentation**: `/admin/docs` - Swagger UI  
- **Health**: `/health`

### 🔗 Direct Service Access
- **Auth Service**: `https://auth-service-[random].onrender.com`
- **Admin Service**: `https://admin-service-[random].onrender.com`

## 🔧 Configuration Features

### APISIX Gateway Features
- ✅ **CORS** - Cross-origin resource sharing
- ⚡ **Rate Limiting** - 10 requests/second, burst 30
- 🗄️ **Caching** - 5-minute cache for GET requests
- 📊 **Metrics** - Prometheus monitoring
- 🔄 **Health Checks** - Automatic upstream monitoring

### Envoy Gateway Features  
- ✅ **CORS** - Cross-origin resource sharing
- 🔍 **Access Logging** - Request/response logging
- 🏥 **Health Checks** - Upstream service monitoring
- 🔄 **Load Balancing** - Round-robin distribution

### Database Features
- 💾 **Persistent Storage** - 10GB for PostgreSQL, 5GB for Redis
- 🔐 **Auto-generated Passwords** - Secure database credentials
- 🏥 **Health Monitoring** - Connection and readiness checks
- 🔄 **Automatic Backups** - Render's built-in backup system

## 📊 Monitoring & Health

All services include comprehensive health checks:

```bash
# Check gateway health
curl https://apisix-gateway-[random].onrender.com/health
curl https://envoy-gateway-[random].onrender.com/health

# Check service health  
curl https://auth-service-[random].onrender.com/health
curl https://admin-service-[random].onrender.com/health
```

Monitor in Render Dashboard:
- 📈 **CPU/Memory Usage**
- 🌐 **Request Metrics** 
- ❌ **Error Rates**
- 🚀 **Deployment Status**

## 🔍 Testing Your Deployment

### 1. Test Auth Service
```bash
# Register a user
curl -X POST "https://apisix-gateway-[random].onrender.com/api/v1/auth/users/" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","first_name":"Test","last_name":"User","password":"TestPass123!","confirm_password":"TestPass123!"}'

# Login
curl -X POST "https://apisix-gateway-[random].onrender.com/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=TestPass123!"
```

### 2. Test Admin Service
```bash
# Check admin health
curl "https://envoy-gateway-[random].onrender.com/health"

# Access admin docs
open "https://envoy-gateway-[random].onrender.com/admin/docs"
```

## 🐛 Troubleshooting

### Common Issues

**1. Service Won't Start**
- ❌ Check environment variables are set
- ❌ Verify RSA keys are properly base64 encoded
- ❌ Check service logs in Render dashboard

**2. Database Connection Failed**
- ❌ Ensure database services are healthy
- ❌ Check connection strings in logs
- ❌ Verify network connectivity

**3. Gateway Routing Issues**
- ❌ Test direct service endpoints first
- ❌ Check gateway configuration
- ❌ Verify service hostnames

### Debug Commands

```bash
# View service logs
render logs auth-service

# Check service status
render status auth-service

# Restart service
render restart auth-service
```

## 💰 Cost Estimation

**Starter Plan** (Development):
- 8 services × $7/month = $56/month
- Databases included in service cost
- Free SSL certificates
- 750 hours/month included

**Standard Plan** (Production):
- Upgrade critical services to Standard
- Better performance and reliability
- More compute resources

## 🔒 Security Features

- 🔐 **HTTPS by Default** - All traffic encrypted
- 🔑 **Environment Variables** - Encrypted at rest
- 🛡️ **Auto-generated Passwords** - Secure database access
- ✅ **CORS Configuration** - Proper cross-origin handling
- ⚡ **Rate Limiting** - DDoS protection

## 📈 Scaling

To scale your deployment:

1. **Vertical Scaling**: Upgrade instance types in Render dashboard
2. **Horizontal Scaling**: Add service replicas (Standard plan+)
3. **Database Scaling**: Upgrade database plans for more storage/performance
4. **CDN**: Add Render's CDN for static assets

## 🎯 Production Checklist

- [ ] Set up custom domains
- [ ] Configure SSL certificates  
- [ ] Set up monitoring alerts
- [ ] Implement backup strategies
- [ ] Configure CI/CD pipelines
- [ ] Set up staging environment
- [ ] Configure log aggregation
- [ ] Set up error tracking

## 📚 Additional Resources

- 📖 **[Full Deployment Guide](deploy-to-render.md)** - Detailed instructions
- 🔧 **[Render Documentation](https://render.com/docs)** - Platform documentation
- 🐛 **[Troubleshooting Guide](deploy-to-render.md#troubleshooting)** - Common issues
- 📊 **[Monitoring Setup](deploy-to-render.md#monitoring--health-checks)** - Health checks

---

🎉 **Your ZCare microservices are now ready for cloud deployment!**

For support, check the deployment logs in Render dashboard or refer to the troubleshooting section.
