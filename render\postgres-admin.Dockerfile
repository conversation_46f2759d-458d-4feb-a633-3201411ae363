FROM postgres:13

# Set environment variables
ENV POSTGRES_USER=postgres
ENV POSTGRES_DB=admin_service
ENV PGDATA=/var/lib/postgresql/data/pgdata

# Create the data directory
RUN mkdir -p /var/lib/postgresql/data/pgdata && \
    chown -R postgres:postgres /var/lib/postgresql/data

# Copy initialization scripts if any
COPY ./admin-service/init.sql /docker-entrypoint-initdb.d/ 2>/dev/null || true

# Expose PostgreSQL port
EXPOSE 5432

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB

# Start PostgreSQL
CMD ["postgres"]
